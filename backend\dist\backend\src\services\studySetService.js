"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.studySetService = exports.StudySetService = void 0;
const supabaseService_1 = require("./supabaseService");
class StudySetService {
    async createStudySet(data) {
        const { data: studySet, error } = await supabaseService_1.supabase
            .from('study_sets')
            .insert({
            user_id: data.user_id,
            name: data.name,
            type: data.type,
            is_ai_generated: data.is_ai_generated,
            source_documents: data.source_documents || null,
            custom_prompt: data.custom_prompt || null,
            total_items: 0
        })
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to create study set: ${error.message}`);
        }
        return studySet;
    }
    async addFlashcardsToSet(studySetId, flashcards) {
        const flashcardInserts = flashcards.map(card => ({
            study_set_id: studySetId,
            front: card.front,
            back: card.back,
            difficulty_level: card.difficulty_level,
            is_ai_generated: card.is_ai_generated,
            times_reviewed: card.times_reviewed || 0,
            last_reviewed_at: card.last_reviewed_at,
            is_flagged: false
        }));
        const { error } = await supabaseService_1.supabase
            .from('flashcards')
            .insert(flashcardInserts);
        if (error) {
            throw new Error(`Failed to add flashcards: ${error.message}`);
        }
    }
    async addQuizQuestionsToSet(studySetId, questions) {
        const questionInserts = questions.map(question => ({
            study_set_id: studySetId,
            question_text: question.question_text,
            question_type: question.question_type,
            options: question.options,
            correct_answers: question.correct_answers,
            explanation: question.explanation,
            difficulty_level: question.difficulty_level,
            is_ai_generated: question.is_ai_generated,
            times_attempted: question.times_attempted || 0,
            times_correct: question.times_correct || 0
        }));
        const { error } = await supabaseService_1.supabase
            .from('quiz_questions')
            .insert(questionInserts);
        if (error) {
            throw new Error(`Failed to add quiz questions: ${error.message}`);
        }
    }
    async getUserStudySets(userId, limit = 50, offset = 0) {
        const { data, error } = await supabaseService_1.supabase
            .from('study_sets')
            .select('*')
            .eq('user_id', userId)
            .order('created_at', { ascending: false })
            .range(offset, offset + limit - 1);
        if (error) {
            throw new Error(`Failed to get study sets: ${error.message}`);
        }
        return data || [];
    }
    async getStudySetById(studySetId, userId) {
        const { data, error } = await supabaseService_1.supabase
            .from('study_sets')
            .select('*')
            .eq('id', studySetId)
            .eq('user_id', userId)
            .single();
        if (error) {
            if (error.code === 'PGRST116')
                return null;
            throw new Error(`Failed to get study set: ${error.message}`);
        }
        return data;
    }
    async deleteStudySet(studySetId, userId) {
        const { error } = await supabaseService_1.supabase
            .from('study_sets')
            .delete()
            .eq('id', studySetId)
            .eq('user_id', userId);
        if (error) {
            throw new Error(`Failed to delete study set: ${error.message}`);
        }
    }
    async updateStudySet(studySetId, userId, updates) {
        const { data, error } = await supabaseService_1.supabase
            .from('study_sets')
            .update(updates)
            .eq('id', studySetId)
            .eq('user_id', userId)
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to update study set: ${error.message}`);
        }
        return data;
    }
    async getFlashcardsByStudySet(studySetId, userId) {
        // First verify the study set belongs to the user
        const studySet = await this.getStudySetById(studySetId, userId);
        if (!studySet) {
            throw new Error('Study set not found or access denied');
        }
        const { data, error } = await supabaseService_1.supabase
            .from('flashcards')
            .select('*')
            .eq('study_set_id', studySetId)
            .order('created_at', { ascending: true });
        if (error) {
            throw new Error(`Failed to get flashcards: ${error.message}`);
        }
        return data || [];
    }
    async getQuizQuestionsByStudySet(studySetId, userId) {
        // First verify the study set belongs to the user
        const studySet = await this.getStudySetById(studySetId, userId);
        if (!studySet) {
            throw new Error('Study set not found or access denied');
        }
        const { data, error } = await supabaseService_1.supabase
            .from('quiz_questions')
            .select('*')
            .eq('study_set_id', studySetId)
            .order('created_at', { ascending: true });
        if (error) {
            throw new Error(`Failed to get quiz questions: ${error.message}`);
        }
        return data || [];
    }
}
exports.StudySetService = StudySetService;
exports.studySetService = new StudySetService();
//# sourceMappingURL=studySetService.js.map