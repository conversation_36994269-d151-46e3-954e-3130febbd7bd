import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AlertDialog } from '../AlertDialog';
import { ConfirmDialog } from '../ConfirmDialog';
import { PromptDialog } from '../PromptDialog';

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    input: ({ children, ...props }: any) => <input {...props}>{children}</input>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

describe('Dialog Accessibility', () => {
  describe('AlertDialog', () => {
    it('should have proper ARIA attributes', () => {
      render(
        <AlertDialog
          isOpen={true}
          onClose={jest.fn()}
          title="Test Alert"
          message="This is a test message"
        />
      );

      const dialog = screen.getByRole('dialog');
      expect(dialog).toHaveAttribute('aria-modal', 'true');
      expect(dialog).toHaveAttribute('aria-labelledby', 'dialog-title');
      expect(dialog).toHaveAttribute('aria-describedby', 'dialog-content');

      expect(screen.getByText('Test Alert')).toHaveAttribute('id', 'dialog-title');
    });

    it('should focus the confirm button on open', async () => {
      render(
        <AlertDialog
          isOpen={true}
          onClose={jest.fn()}
          title="Test Alert"
          message="This is a test message"
        />
      );

      await waitFor(() => {
        const confirmButton = screen.getByRole('button', { name: /ok/i });
        expect(confirmButton).toHaveFocus();
      });
    });

    it('should close on Escape key', async () => {
      const onClose = jest.fn();
      render(
        <AlertDialog
          isOpen={true}
          onClose={onClose}
          title="Test Alert"
          message="This is a test message"
        />
      );

      fireEvent.keyDown(document, { key: 'Escape' });
      expect(onClose).toHaveBeenCalled();
    });

    it('should have decorative icons marked as aria-hidden', () => {
      render(
        <AlertDialog
          isOpen={true}
          onClose={jest.fn()}
          title="Test Alert"
          message="This is a test message"
          variant="success"
        />
      );

      const icon = screen.getByRole('dialog').querySelector('svg');
      expect(icon).toHaveAttribute('aria-hidden', 'true');
    });
  });

  describe('ConfirmDialog', () => {
    it('should have proper ARIA attributes', () => {
      render(
        <ConfirmDialog
          isOpen={true}
          onClose={jest.fn()}
          onConfirm={jest.fn()}
          title="Test Confirm"
          message="Are you sure?"
        />
      );

      const dialog = screen.getByRole('dialog');
      expect(dialog).toHaveAttribute('aria-modal', 'true');
      expect(dialog).toHaveAttribute('aria-labelledby', 'dialog-title');
    });

    it('should focus the confirm button by default', async () => {
      render(
        <ConfirmDialog
          isOpen={true}
          onClose={jest.fn()}
          onConfirm={jest.fn()}
          title="Test Confirm"
          message="Are you sure?"
        />
      );

      await waitFor(() => {
        const confirmButton = screen.getByRole('button', { name: /confirm/i });
        expect(confirmButton).toHaveFocus();
      });
    });

    it('should handle keyboard navigation between buttons', async () => {
      const user = userEvent.setup();
      render(
        <ConfirmDialog
          isOpen={true}
          onClose={jest.fn()}
          onConfirm={jest.fn()}
          title="Test Confirm"
          message="Are you sure?"
        />
      );

      const confirmButton = screen.getByRole('button', { name: /confirm/i });
      const cancelButton = screen.getByRole('button', { name: /cancel/i });

      // Tab should move between buttons
      await user.tab();
      expect(cancelButton).toHaveFocus();

      await user.tab();
      expect(confirmButton).toHaveFocus();
    });
  });

  describe('PromptDialog', () => {
    it('should have proper ARIA attributes on input', () => {
      render(
        <PromptDialog
          isOpen={true}
          onClose={jest.fn()}
          onConfirm={jest.fn()}
          title="Test Prompt"
          message="Enter your name:"
          placeholder="Your name"
        />
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('aria-required', 'true');
      expect(input).toHaveAttribute('aria-label', 'Your name');
      expect(input).toHaveAttribute('aria-invalid', 'false');
    });

    it('should focus the input field on open', async () => {
      render(
        <PromptDialog
          isOpen={true}
          onClose={jest.fn()}
          onConfirm={jest.fn()}
          title="Test Prompt"
          message="Enter your name:"
        />
      );

      await waitFor(() => {
        const input = screen.getByRole('textbox');
        expect(input).toHaveFocus();
      });
    });

    it('should show error with proper ARIA attributes', async () => {
      const validation = (value: string) => value.length < 3 ? 'Too short' : null;
      
      render(
        <PromptDialog
          isOpen={true}
          onClose={jest.fn()}
          onConfirm={jest.fn()}
          title="Test Prompt"
          message="Enter your name:"
          validation={validation}
        />
      );

      const input = screen.getByRole('textbox');
      await userEvent.type(input, 'ab');

      await waitFor(() => {
        expect(input).toHaveAttribute('aria-invalid', 'true');
        expect(input).toHaveAttribute('aria-describedby', 'input-error');
        
        const errorMessage = screen.getByRole('alert');
        expect(errorMessage).toHaveAttribute('id', 'input-error');
        expect(errorMessage).toHaveTextContent('Too short');
      });
    });

    it('should submit on Enter key when valid', async () => {
      const onConfirm = jest.fn();
      render(
        <PromptDialog
          isOpen={true}
          onClose={jest.fn()}
          onConfirm={onConfirm}
          title="Test Prompt"
          message="Enter your name:"
        />
      );

      const input = screen.getByRole('textbox');
      await userEvent.type(input, 'John Doe');
      await userEvent.keyboard('{Enter}');

      expect(onConfirm).toHaveBeenCalledWith('John Doe');
    });
  });

  describe('Focus Management', () => {
    it('should trap focus within dialog', async () => {
      const user = userEvent.setup();
      render(
        <ConfirmDialog
          isOpen={true}
          onClose={jest.fn()}
          onConfirm={jest.fn()}
          title="Test Focus Trap"
          message="Focus should be trapped"
        />
      );

      const confirmButton = screen.getByRole('button', { name: /confirm/i });
      const cancelButton = screen.getByRole('button', { name: /cancel/i });

      // Start at confirm button
      confirmButton.focus();
      expect(confirmButton).toHaveFocus();

      // Tab forward should go to cancel
      await user.tab();
      expect(cancelButton).toHaveFocus();

      // Tab forward again should wrap to confirm
      await user.tab();
      expect(confirmButton).toHaveFocus();

      // Shift+Tab should go back to cancel
      await user.tab({ shift: true });
      expect(cancelButton).toHaveFocus();
    });
  });
});
