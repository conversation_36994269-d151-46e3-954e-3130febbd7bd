import React, { Suspense, lazy } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
  useLocation,
} from "react-router-dom";
import { LoginForm } from "./components/auth/LoginForm";
import { SignupForm } from "./components/auth/SignupForm";
import { ProtectedRoute } from "./components/auth/ProtectedRoute";
import { SkipNavigation } from "./components/accessibility/SkipNavigation";
import { KeyboardShortcutsModal } from "./components/ui/KeyboardShortcutsModal";
import { useKeyboardShortcuts } from "./hooks/useKeyboardShortcuts";
import { DialogProvider } from "./contexts/DialogContext";

// Lazy load heavy components
const Dashboard = lazy(() => import("./components/dashboard/Dashboard").then(module => ({ default: module.Dashboard })));
const DocumentsPage = lazy(() => import("./pages/DocumentsPage").then(module => ({ default: module.DocumentsPage })));
const GenerateFlashcardsPage = lazy(() => import("./pages/GenerateFlashcardsPage").then(module => ({ default: module.GenerateFlashcardsPage })));
const GenerateQuizPage = lazy(() => import("./pages/GenerateQuizPage").then(module => ({ default: module.GenerateQuizPage })));
const StudySetPage = lazy(() => import("./pages/StudySetPage").then(module => ({ default: module.StudySetPage })));
const StudyPage = lazy(() => import("./pages/StudyPage").then(module => ({ default: module.StudyPage })));
const AnalyticsPage = lazy(() => import("./pages/AnalyticsPage").then(module => ({ default: module.AnalyticsPage })));

const AppContent = () => {
  const location = useLocation();
  const { showShortcutsModal, setShowShortcutsModal } = useKeyboardShortcuts();

  // Define skip navigation links based on current route
  const getSkipLinks = () => {
    const path = location.pathname;
    if (path === '/dashboard') {
      return [
        { href: '#main-content', label: 'Skip to main content' },
        { href: '#study-sets', label: 'Skip to study sets' },
        { href: '#quick-actions', label: 'Skip to quick actions' }
      ];
    } else if (path.startsWith('/study/')) {
      return [
        { href: '#main-content', label: 'Skip to main content' },
        { href: '#flashcard', label: 'Skip to flashcard' },
        { href: '#navigation-controls', label: 'Skip to navigation controls' }
      ];
    } else if (path === '/documents') {
      return [
        { href: '#main-content', label: 'Skip to main content' },
        { href: '#upload-section', label: 'Skip to upload section' },
        { href: '#document-list', label: 'Skip to document list' }
      ];
    }
    return [{ href: '#main-content', label: 'Skip to main content' }];
  };

  return (
    <>
      <SkipNavigation links={getSkipLinks()} />
      <Suspense fallback={
        <div className="min-h-screen bg-background-primary flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading...</p>
          </div>
        </div>
      }>
        <Routes>
        <Route path="/" element={<Navigate to="/login" />} />
        <Route path="/login" element={<LoginForm />} />
        <Route path="/signup" element={<SignupForm />} />
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="/documents"
          element={
            <ProtectedRoute>
              <DocumentsPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="/generate/flashcards"
          element={
            <ProtectedRoute>
              <GenerateFlashcardsPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="/generate/quiz"
          element={
            <ProtectedRoute>
              <GenerateQuizPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="/study-sets/:id"
          element={
            <ProtectedRoute>
              <StudySetPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="/study/:id/:mode"
          element={
            <ProtectedRoute>
              <StudyPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="/analytics"
          element={
            <ProtectedRoute>
              <AnalyticsPage />
            </ProtectedRoute>
          }
        />
        </Routes>
      </Suspense>

      {/* Global Keyboard Shortcuts Modal */}
      <KeyboardShortcutsModal
        isOpen={showShortcutsModal}
        onClose={() => setShowShortcutsModal(false)}
      />
    </>
  );
};

function App() {
  return (
    <Router>
      <DialogProvider>
        <AppContent />
      </DialogProvider>
    </Router>
  );
}

export default App;
