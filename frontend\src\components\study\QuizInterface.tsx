import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useStudyStore } from '../../stores/studyStore';
import { useDialog } from '../../contexts/DialogContext';
import { Button } from '../common/Button';



export const QuizInterface: React.FC = () => {
  const navigate = useNavigate();
  const { alert } = useDialog();
  const {
    currentSession,
    studySetContent,
    nextItem,
    submitQuizAnswer,
    endStudySession,
    updateTimeSpent
  } = useStudyStore();

  const [selectedAnswers, setSelectedAnswers] = useState<string[]>([]);
  const [hasAnswered, setHasAnswered] = useState(false);
  const [showExplanation, setShowExplanation] = useState(false);



  useEffect(() => {
    const interval = setInterval(() => {
      updateTimeSpent(1);
    }, 1000);

    return () => clearInterval(interval);
  }, [updateTimeSpent]);

  useEffect(() => {
    // Reset state when moving to new question
    setSelectedAnswers([]);
    setHasAnswered(false);
    setShowExplanation(false);

  }, [currentSession?.currentIndex]);

  if (!currentSession || !studySetContent?.questions) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">No quiz session found</div>
          <Button onClick={() => navigate('/dashboard')} variant="secondary">
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  const questions = studySetContent.questions;
  const currentQuestion = questions[currentSession.currentIndex];
  const progress = ((currentSession.currentIndex + 1) / currentSession.totalItems) * 100;
  const isLastQuestion = currentSession.currentIndex === currentSession.totalItems - 1;

  const handleAnswerSelect = (answer: string) => {
    if (hasAnswered) return;

    if (currentQuestion.question_type === 'multiple_choice' || currentQuestion.question_type === 'true_false') {
      setSelectedAnswers([answer]);
    } else if (currentQuestion.question_type === 'select_all') {
      setSelectedAnswers(prev => 
        prev.includes(answer) 
          ? prev.filter(a => a !== answer)
          : [...prev, answer]
      );
    }
  };

  const handleShortAnswerChange = (value: string) => {
    if (hasAnswered) return;
    setSelectedAnswers([value]);
  };

  const handleSubmitAnswer = () => {
    if (hasAnswered || selectedAnswers.length === 0) return;

    const isCorrect = checkAnswer();

    submitQuizAnswer(currentQuestion.id, selectedAnswers, isCorrect);
    setHasAnswered(true);
    setShowExplanation(true);
  };

  const checkAnswer = (): boolean => {
    const correctAnswers = currentQuestion.correct_answers;
    
    if (currentQuestion.question_type === 'short_answer') {
      // For short answer, check if any correct answer is contained in the user's answer
      const userAnswer = selectedAnswers[0]?.toLowerCase().trim() || '';
      return correctAnswers.some(correct => 
        userAnswer.includes(correct.toLowerCase().trim()) ||
        correct.toLowerCase().trim().includes(userAnswer)
      );
    } else {
      // For other types, exact match required
      return selectedAnswers.length === correctAnswers.length &&
             selectedAnswers.every(answer => correctAnswers.includes(answer));
    }
  };

  const handleNext = () => {
    if (isLastQuestion) {
      handleFinishQuiz();
    } else {
      nextItem();
    }
  };

  const handleFinishQuiz = async () => {
    const totalQuestions = currentSession.totalItems;
    const correctAnswers = currentSession.correctAnswers || 0;
    const percentage = Math.round((correctAnswers / totalQuestions) * 100);
    const timeSpent = currentSession.timeSpent;

    endStudySession();

    // Show results modal or navigate with results
    await alert({
      title: 'Quiz Complete!',
      message: `Score: ${correctAnswers}/${totalQuestions} (${percentage}%)\nTime spent: ${Math.floor(timeSpent / 60)}m ${timeSpent % 60}s`,
      variant: 'success',
      confirmText: 'Continue'
    });

    navigate(`/study-sets/${currentSession.studySetId}`);
  };

  const renderQuestionOptions = () => {
    if (!currentQuestion.options) return null;

    return currentQuestion.options.map((option, index) => {
      const isSelected = selectedAnswers.includes(option);
      const isCorrect = currentQuestion.correct_answers.includes(option);
      
      let buttonClass = 'w-full text-left p-4 rounded-lg border transition-all ';
      
      if (hasAnswered) {
        if (isCorrect) {
          buttonClass += 'border-green-500 bg-green-500/20 text-green-400';
        } else if (isSelected && !isCorrect) {
          buttonClass += 'border-red-500 bg-red-500/20 text-red-400';
        } else {
          buttonClass += 'border-gray-600 bg-background-secondary text-gray-400';
        }
      } else {
        if (isSelected) {
          buttonClass += 'border-primary-500 bg-primary-500/20 text-primary-400';
        } else {
          buttonClass += 'border-gray-600 bg-background-secondary text-white hover:border-gray-500';
        }
      }

      return (
        <button
          key={index}
          onClick={() => handleAnswerSelect(option)}
          disabled={hasAnswered}
          className={buttonClass}
        >
          <div className="flex items-center space-x-3">
            <div className={`
              w-5 h-5 rounded border-2 flex items-center justify-center
              ${isSelected ? 'border-current' : 'border-gray-500'}
            `}>
              {isSelected && (
                <div className="w-2 h-2 rounded bg-current"></div>
              )}
            </div>
            <span>{option}</span>
          </div>
        </button>
      );
    });
  };

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <button
          onClick={() => navigate(`/study-sets/${currentSession.studySetId}`)}
          className="text-gray-400 hover:text-white flex items-center"
        >
          ← Back to Study Set
        </button>
        
        <div className="text-center">
          <h1 className="text-xl font-semibold text-white">
            {studySetContent.studySet?.name}
          </h1>
          <p className="text-sm text-gray-400">
            Question {currentSession.currentIndex + 1} of {currentSession.totalItems}
          </p>
        </div>

        <Button
          onClick={handleFinishQuiz}
          variant="secondary"
          size="sm"
        >
          Finish Quiz
        </Button>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div 
            className="bg-primary-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <div className="flex justify-between text-xs text-gray-400 mt-1">
          <span>Progress: {Math.round(progress)}%</span>
          <span>Score: {currentSession.correctAnswers || 0}/{currentSession.currentIndex + (hasAnswered ? 1 : 0)}</span>
          <span>Time: {Math.floor(currentSession.timeSpent / 60)}:{(currentSession.timeSpent % 60).toString().padStart(2, '0')}</span>
        </div>
      </div>

      {/* Question */}
      <div className="bg-background-secondary rounded-lg p-6 mb-6">
        <div className="mb-4">
          <span className="text-sm text-gray-400 uppercase tracking-wide">
            {currentQuestion.question_type.replace('_', ' ')}
          </span>
        </div>
        
        <h2 className="text-xl text-white mb-6 leading-relaxed">
          {currentQuestion.question_text}
        </h2>

        {/* Answer Options */}
        <div className="space-y-3">
          {currentQuestion.question_type === 'short_answer' ? (
            <textarea
              value={selectedAnswers[0] || ''}
              onChange={(e) => handleShortAnswerChange(e.target.value)}
              disabled={hasAnswered}
              placeholder="Type your answer here..."
              rows={3}
              className="w-full px-4 py-3 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50"
            />
          ) : (
            renderQuestionOptions()
          )}
        </div>

        {/* Submit Button */}
        {!hasAnswered && (
          <div className="mt-6">
            <Button
              onClick={handleSubmitAnswer}
              disabled={selectedAnswers.length === 0}
              className="w-full"
            >
              Submit Answer
            </Button>
          </div>
        )}

        {/* Explanation */}
        {hasAnswered && showExplanation && currentQuestion.explanation && (
          <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
            <h4 className="text-blue-400 font-medium mb-2">Explanation</h4>
            <p className="text-gray-300">{currentQuestion.explanation}</p>
          </div>
        )}
      </div>

      {/* Navigation */}
      {hasAnswered && (
        <div className="flex justify-center">
          <Button
            onClick={handleNext}
            variant="primary"
            size="lg"
          >
            {isLastQuestion ? 'Finish Quiz' : 'Next Question →'}
          </Button>
        </div>
      )}
    </div>
  );
};
