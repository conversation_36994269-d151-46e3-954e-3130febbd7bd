import React from 'react';
import { StudyAnalytics } from '../components/analytics/StudyAnalytics';

// Mock data for demonstration - in a real app, this would come from your backend
const mockStudySets = [
  { id: '1', title: 'Spanish Vocabulary', description: 'Basic Spanish words', createdAt: new Date(), updatedAt: new Date() },
  { id: '2', title: 'Math Formulas', description: 'Algebra and geometry', createdAt: new Date(), updatedAt: new Date() },
  { id: '3', title: 'History Facts', description: 'World War II timeline', createdAt: new Date(), updatedAt: new Date() }
];

const mockSessions = [
  {
    id: '1',
    studySetId: '1',
    type: 'flashcards' as const,
    startTime: new Date(Date.now() - 86400000), // 1 day ago
    totalItems: 20,
    reviewedItems: 18,
    flaggedItems: 3,
    timeSpent: 1200 // 20 minutes
  },
  {
    id: '2',
    studySetId: '2',
    type: 'quiz' as const,
    startTime: new Date(Date.now() - 172800000), // 2 days ago
    totalItems: 15,
    reviewedItems: 15,
    flaggedItems: 0,
    correctAnswers: 12,
    timeSpent: 900 // 15 minutes
  },
  {
    id: '3',
    studySetId: '1',
    type: 'flashcards' as const,
    startTime: new Date(Date.now() - 259200000), // 3 days ago
    totalItems: 20,
    reviewedItems: 20,
    flaggedItems: 5,
    timeSpent: 1800 // 30 minutes
  },
  {
    id: '4',
    studySetId: '3',
    type: 'quiz' as const,
    startTime: new Date(Date.now() - 345600000), // 4 days ago
    totalItems: 25,
    reviewedItems: 25,
    flaggedItems: 0,
    correctAnswers: 20,
    timeSpent: 1500 // 25 minutes
  },
  {
    id: '5',
    studySetId: '2',
    type: 'flashcards' as const,
    startTime: new Date(Date.now() - 432000000), // 5 days ago
    totalItems: 15,
    reviewedItems: 12,
    flaggedItems: 2,
    timeSpent: 600 // 10 minutes
  }
];

export const AnalyticsPage: React.FC = () => {
  return (
    <StudyAnalytics 
      studySets={mockStudySets}
      sessions={mockSessions}
    />
  );
};
